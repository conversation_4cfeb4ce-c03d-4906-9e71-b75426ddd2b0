@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 210 22% 22%;
    /* #27374D - Natural dark blue-gray */

    --card: 0 0% 100%;
    --card-foreground: 210 22% 22%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 22% 22%;

    --primary: 210 22% 32%;
    /* #526D82 - Natural blue-gray */
    --primary-foreground: 0 0% 98%;

    --secondary: 210 25% 95%;
    /* Light natural blue-gray */
    --secondary-foreground: 210 22% 22%;

    --muted: 210 25% 96%;
    /* Very light natural blue-gray */
    --muted-foreground: 210 15% 55%;
    /* Softer muted text */

    --accent: 210 25% 94%;
    /* Light accent */
    --accent-foreground: 210 22% 22%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 210 20% 90%;
    /* Light natural border */
    --input: 210 20% 92%;
    /* Light input background */
    --ring: 210 22% 32%;
    /* Primary color for focus rings */

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 210 22% 8%;
    /* Dark natural background */
    --foreground: 0 0% 95%;

    --card: 210 22% 10%;
    /* Dark card background */
    --card-foreground: 0 0% 95%;

    --popover: 210 22% 10%;
    --popover-foreground: 0 0% 95%;

    --primary: 210 22% 45%;
    /* Lighter version of #526D82 for dark mode */
    --primary-foreground: 0 0% 98%;

    --secondary: 210 22% 15%;
    /* Dark secondary */
    --secondary-foreground: 0 0% 95%;

    --muted: 210 22% 12%;
    /* Dark muted */
    --muted-foreground: 210 15% 70%;
    /* Lighter muted text for dark mode */

    --accent: 210 22% 15%;
    /* Dark accent */
    --accent-foreground: 0 0% 95%;

    --destructive: 0 62.8% 50%;
    /* Slightly lighter destructive for dark mode */
    --destructive-foreground: 0 0% 98%;

    --border: 210 22% 18%;
    /* Dark border */
    --input: 210 22% 15%;
    /* Dark input background */
    --ring: 210 22% 45%;
    /* Primary color for focus rings */

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {

  /* Natural color utilities based on user preferences */
  .bg-natural-primary {
    background-color: #526D82;
  }

  .bg-natural-secondary {
    background-color: #27374D;
  }

  .text-natural-primary {
    color: #526D82;
  }

  .text-natural-secondary {
    color: #27374D;
  }

  .border-natural-primary {
    border-color: #526D82;
  }

  .border-natural-secondary {
    border-color: #27374D;
  }

  /* Light variations for professional appearance */
  .bg-natural-light {
    background-color: #f8f9fb;
  }

  .bg-natural-muted {
    background-color: #f1f3f6;
  }
}